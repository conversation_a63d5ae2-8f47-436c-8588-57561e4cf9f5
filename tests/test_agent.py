import os
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
from services.ia_agents.entry_signal_agent import entry_signal_agent
from constants.prompt_templates import PromptType, TradingPromptTemplates
from langchain_core.prompts import ChatPromptTemplate

prompt_templates = TradingPromptTemplates()
prompt = prompt_templates.get_template(PromptType.ENTRY_ANALYSIS)
template = ChatPromptTemplate.from_template(
    template=prompt.template,
    partial_variables={"format_instructions": "Return JSON format..."}
)

agent = entry_signal_agent(template)
print(agent.invoke(  {"messages": [{"role": "user", "content": "is good for entry?"}]}))
