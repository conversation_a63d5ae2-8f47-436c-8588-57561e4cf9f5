from langgraph.prebuilt import create_react_agent
from langchain_core.prompts import ChatPromptTemplate
from constants.openai_models import OpenAIModels
from services.ta_services import TAService
from services.binance_client import BinanceClient
from utils.load_config import load_config
from models.IA import EntryAnalysis
from dotenv import load_dotenv
load_dotenv()

config = load_config("src/config/config.yaml")
binance_client = BinanceClient(config)
technical_indicators = TAService(binance_client)


def entry_signal_agent(prompt: ChatPromptTemplate):
    return create_react_agent(
        model="openai:gpt-5-mini",
        tools=[technical_indicators.get_market_indicators],
        prompt=prompt,
        name="entry_signal_agent",
        response_format=EntryAnalysis
    )
