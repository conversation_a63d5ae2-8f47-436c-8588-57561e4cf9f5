import logging
from typing import Optional, Dict, Any
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import JsonOutputParser
from langchain_openai import ChatOpenAI
from langchain_core.memory import ConversationSummaryMemory
from models.IA.PositionAnalysis import PositionAnalysis
from models.IA.EntryAnalysis import EntryAnalysis
from utils import google_news
import utils.fear_and_greed_utils as fg
import time
from models.Indicators import MarketIndicators
from constants.openai_models import OpenAIModels
from constants.prompt_templates import TradingPromptTemplates, PromptType

logger = logging.getLogger()


# Define the desired JSON structure for the AI's response for an OPEN position
class AIEngine:
    def __init__(self, config):
        self.config = config
        self.llm = ChatOpenAI(
            temperature=1, model=OpenAIModels.GPT_5_NANO
        )  # Real implementation
        self.entry_parser = JsonOutputParser(pydantic_object=EntryAnalysis)
        self.position_parser = JsonOutputParser(pydantic_object=PositionAnalysis)
        self.memory = ConversationSummaryMemory(llm=llm, memory_key="history")

        # Initialize prompt templates using the new architecture
        self.prompt_templates = TradingPromptTemplates()
        self.entry_prompt = self._create_prompt_from_template(PromptType.ENTRY_ANALYSIS)
        self.position_prompt = self._create_prompt_from_template(PromptType.POSITION_ANALYSIS)

        self.news_fetcher = google_news

        # Context management
        self.max_context_length = 3000  # Maximum characters for context
        self.context_cache = {}
        self.cache_duration = 300  # 5 minutes cache

    def _create_prompt_from_template(self, prompt_type: PromptType) -> ChatPromptTemplate:
        """
        Create a ChatPromptTemplate from a template type.

        Args:
            prompt_type: The type of prompt template to create

        Returns:
            ChatPromptTemplate: Configured prompt template
        """
        template = self.prompt_templates.get_template(prompt_type)

        # Determine which parser to use based on prompt type
        if prompt_type == PromptType.ENTRY_ANALYSIS:
            format_instructions = self.entry_parser.get_format_instructions()
        else:  # POSITION_ANALYSIS or STARTUP_ANALYSIS
            format_instructions = self.position_parser.get_format_instructions()

        return ChatPromptTemplate.from_template(
            template=template.template,
            partial_variables={"format_instructions": format_instructions}
        )



    def analyze_for_new_entry(self, market_indicators: MarketIndicators):
        """Queries the LLM for analysis on whether to enter a new trade."""
        if not self.config["ai_enabled"]:
            return {"market_sentiment": "Neutral", "is_entry_favorable": False}

        logger.info("Querying AI for new entry analysis...")

        # Get market context with caching and truncation
        market_context = self._get_market_context()

        prompt_data = {
            "trading_pair": self.config["trading_pair"],
            "current_price": market_indicators.price_indicators.current_price,
            "avg_price": market_indicators.price_indicators.avg_price,
            "median_price": market_indicators.price_indicators.median_price,
            "ema": market_indicators.overlap_studies.ema,
            "sma": market_indicators.overlap_studies.sma,
            "sar": market_indicators.overlap_studies.sar,
            "bband_upper": market_indicators.overlap_studies.bbands["upper"],
            "bband_middle": market_indicators.overlap_studies.bbands["middle"],
            "bband_lower": market_indicators.overlap_studies.bbands["lower"],
            "rsi": market_indicators.momentum_indicators.rsi,
            "atr": market_indicators.volatility_indicators.atr,
            "news_headlines": market_context["news_headlines"],
            "sentiment_score": market_context["sentiment_score"],
        }

        chain = self.entry_prompt | self.llm | self.entry_parser
        try:
            result = chain.invoke(prompt_data)
            favorable_sentiments = ["Bullish", "Very Bullish"]
            result["is_entry_favorable"] = (
                result["market_sentiment"] in favorable_sentiments
                and result["confidence_score"] >= 0.6
            )
            logger.info(f"AI Entry Analysis received: {result}")
            return result
        except Exception as e:
            logger.error(f"Error calling AI for entry analysis: {e}")
            return {"market_sentiment": "Neutral", "is_entry_favorable": False}

    def analyze_open_position(self, position, market_indicators: MarketIndicators):
        """Queries the LLM for analysis on an existing trade."""
        if not self.config["ai_enabled"]:
            return {"action": "hold", "reasoning": "AI disabled."}

        current_price = float(current_price)
        logger.info("Querying AI for open position analysis...")

        pnl_percentage = (
            (current_price - position["average_price"]) / position["average_price"]
        ) * 100

         # Get market context with caching and truncation
        market_context = self._get_market_context()

        prompt_data = {
            "entry_price": position["entry_price"],
            "take_profit_price": position["take_profit_price"],
            "trailing_stop_price": position["trailing_stop_price"],
            "pnl_percentage": f"{pnl_percentage:.2f}",
            "trading_pair": self.config["trading_pair"],
            "current_price": market_indicators.price_indicators.current_price,
            "avg_price": market_indicators.price_indicators.avg_price,
            "median_price": market_indicators.price_indicators.median_price,
            "ema": market_indicators.overlap_studies.ema,
            "sma": market_indicators.overlap_studies.sma,
            "sar": market_indicators.overlap_studies.sar,
            "bband_upper": market_indicators.overlap_studies.bbands["upper"],
            "bband_middle": market_indicators.overlap_studies.bbands["middle"],
            "bband_lower": market_indicators.overlap_studies.bbands["lower"],
            "rsi": market_indicators.momentum_indicators.rsi,
            "atr": market_indicators.volatility_indicators.atr,
            "news_headlines": market_context["news_headlines"],
            "sentiment_score": market_context["sentiment_score"],
        }


        chain = self.position_prompt | self.llm | self.position_parser
        try:
            result = chain.invoke(prompt_data)
            logger.info(f"AI Position Analysis received: {result}")
            return result
        except Exception as e:
            logger.error(f"Error calling AI for position analysis: {e}")
            return {"action": "hold", "reasoning": "AI call failed."}

    def _truncate_context(self, text: str, max_length: int = None) -> str:
        """Truncate text to avoid context overflow while preserving important information."""
        if max_length is None:
            max_length = self.max_context_length

        if len(text) <= max_length:
            return text

        # Try to truncate at sentence boundaries
        sentences = text.split(". ")
        truncated = ""

        for sentence in sentences:
            if len(truncated + sentence + ". ") <= max_length - 50:  # Leave some buffer
                truncated += sentence + ". "
            else:
                break

        if not truncated:  # If no complete sentences fit, just truncate
            truncated = text[: max_length - 3] + "..."
        else:
            truncated += "..."

        return truncated

    def _get_cached_context(self, cache_key: str) -> Optional[Dict]:
        """Get cached context if still valid."""
        if cache_key in self.context_cache:
            cached_data, timestamp = self.context_cache[cache_key]
            if time.time() - timestamp < self.cache_duration:
                return cached_data
            else:
                # Remove expired cache
                del self.context_cache[cache_key]
        return None

    def _cache_context(self, cache_key: str, data: Dict):
        """Cache context data with timestamp."""
        self.context_cache[cache_key] = (data, time.time())

    def _get_market_context(
        self, include_news: bool = True, include_sentiment: bool = True
    ) -> Dict:
        """Get comprehensive market context with caching and truncation."""
        cache_key = f"market_context_{include_news}_{include_sentiment}"

        # Try to get from cache first
        cached = self._get_cached_context(cache_key)
        if cached:
            logger.debug("Using cached market context")
            return cached

        context = {}

        # Get news headlines
        if include_news:
            try:
                query = '("cryptocurrency" OR "crypto" OR "blockchain" OR "bitcoin" OR "ethereum" OR "altcoin" OR "DeFi" OR "NFT" OR "stablecoin") AND ("price" OR "market" OR "regulation" OR "update" OR "analysis" OR "trading" OR "news")'
                news_headlines = self.news_fetcher.get_news(query)
                if news_headlines:
                    # Truncate news to avoid overflow
                    news_text = str(news_headlines)
                    context["news_headlines"] = self._truncate_context(news_text, 1000)
                else:
                    context["news_headlines"] = "No recent news available"
            except Exception as e:
                logger.error(f"Error fetching news: {e}")
                context["news_headlines"] = "Error fetching news"

        # Get sentiment data
        if include_sentiment:
            try:
                fear_greed = fg.get_fear_and_greed_index()
                if fear_greed:
                    context["sentiment_score"] = (
                        f"{fear_greed.get('value', 'N/A')} ({fear_greed.get('value_classification', 'Unknown')})"
                    )
                else:
                    context["sentiment_score"] = "Sentiment data unavailable"
            except Exception as e:
                logger.error(f"Error fetching sentiment: {e}")
                context["sentiment_score"] = "Error fetching sentiment"

        # Cache the context
        self._cache_context(cache_key, context)

        return context

    def analyze_startup_position(
        self, position: Dict, current_price: float, rsi: float
    ) -> Dict:
        """Analyze existing position when bot starts up."""
        if not self.config["ai_enabled"]:
            return {"action": "hold", "reasoning": "AI disabled."}

        logger.info("Analyzing existing position at startup...")

        current_price = float(current_price)
        pnl_percentage = (
            (current_price - position["average_price"]) / position["average_price"]
        ) * 100

        # Get market context with truncation
        market_context = self._get_market_context()

        # Calculate position metrics
        holding_time_hours = 0
        if position.get("last_ai_check_ts"):
            holding_time_hours = (time.time() - position["last_ai_check_ts"]) / 3600

        # Prepare market data with context management
        market_data = {
            "trading_pair": self.config["trading_pair"],
            "average_price": position["average_price"],
            "pnl_percentage": f"{pnl_percentage:.2f}",
            "dca_orders": len(position["dca_orders"]),
            "current_price": current_price,
            "holding_time_hours": f"{holding_time_hours:.1f}",
            "rsi": str(rsi),
            "startup_analysis": "true",
        }

        # Add market context
        market_data.update(market_context)

        # Use the new startup analysis template
        startup_prompt = self._create_prompt_from_template(PromptType.STARTUP_ANALYSIS)
        chain = startup_prompt | self.llm | self.position_parser

        try:
            result = chain.invoke(market_data)
            result["startup_analysis"] = True
            logger.info(f"Startup position analysis completed: {result}")
            return result
        except Exception as e:
            logger.error(f"Error in startup position analysis: {e}")
            return {
                "action": "hold",
                "reasoning": "Startup analysis failed, holding position.",
            }

    def get_context_summary(self) -> Dict:
        """Get a summary of current context cache status."""
        return {
            "cached_items": len(self.context_cache),
            "cache_duration": self.cache_duration,
            "max_context_length": self.max_context_length,
            "cache_keys": list(self.context_cache.keys()),
        }

    def get_available_templates(self) -> Dict[str, str]:
        """
        Get information about available prompt templates.

        Returns:
            Dict[str, str]: Mapping of template names to descriptions
        """
        return self.prompt_templates.get_available_templates()

    def validate_prompt_data(self, prompt_type: PromptType, data: Dict[str, Any]) -> bool:
        """
        Validate that prompt data contains all required variables.

        Args:
            prompt_type: The type of prompt to validate against
            data: The data dictionary to validate

        Returns:
            bool: True if validation passes

        Raises:
            ValueError: If required variables are missing
        """
        template = self.prompt_templates.get_template(prompt_type)
        return self.prompt_templates.validate_template_variables(template, data)
