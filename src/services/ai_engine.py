import logging
from typing import Optional, Dict, List, Any
from langchain_core.prompts import <PERSON>tPromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from langchain_openai import ChatOpenAI
from models.IA.PositionAnalysis import PositionAnalysis
from models.IA.EntryAnalysis import EntryAnalysis
from utils import google_news
import utils.fear_and_greed_utils as fg
import time
from datetime import datetime, timedelta
from models.Indicators import MarketIndicators
from constants.openai_models import OpenAIModels

logger = logging.getLogger()


# Define the desired JSON structure for the AI's response for an OPEN position
class AIEngine:
    def __init__(self, config):
        self.config = config
        self.llm = ChatOpenAI(
            temperature=1, model=OpenAIModels.GPT_5_NANO
        )  # Real implementation
        self.entry_parser = JsonOutputParser(pydantic_object=EntryAnalysis)
        self.position_parser = JsonOutputParser(pydantic_object=PositionAnalysis)
        self.entry_prompt = self._create_advanced_prompt_template()
        self.position_prompt = self._create_position_prompt_template()
        self.news_fetcher = google_news

        # Context management
        self.max_context_length = 3000  # Maximum characters for context
        self.context_cache = {}
        self.cache_duration = 300  # 5 minutes cache
    
    def _create_advanced_prompt_template(self):
        prompt_str = """
        You are an expert technical analyst for a quantitative trading bot. Your task is to analyze a complete snapshot of market indicators for {trading_pair} and provide a structured recommendation for a NEW position: BUY or WAIT for better indicators.

        **Format Instructions:**
        {format_instructions}

        **Market Data Report:**
        - **Price Indicators:**
        - Current Price: {current_price}
        - Average Price (Period): {avg_price}
        - Median Price (Period): {median_price}
        - **Trend & Overlap Studies:**
        - EMA (200p): {ema}
        - SMA (Period): {sma}
        - Parabolic SAR: {sar}
        - Bollinger Bands:
            - Upper: {bband_upper}
            - Middle: {bband_middle}
            - Lower: {bband_lower}
        - **Momentum Indicators:**
        - RSI (14p): {rsi}
        - **Volatility Indicators:**
        - ATR (14p): {atr}
        - News: {news_headlines}
        - Market Sentiment: {sentiment_score}

        **Decision-Making Heuristics:**
        1.  **Determine the Main Trend:** First, compare the `current_price` to the `ema`. A price above the EMA suggests a general uptrend (favorable for BUYs). A price below suggests a downtrend (favorable for SELLs or HOLD).
        2.  **Assess Momentum:** Check the `rsi`. An RSI below 35 suggests oversold conditions (potential BUY opportunity). An RSI above 65 suggests overbought conditions (potential SELL opportunity).
        3.  **Identify Entry/Exit Points:** Look at the Bollinger Bands. The price touching the `lower` band is a strong support level and reinforces a BUY signal, especially in an uptrend. The price touching the `upper` band reinforces a SELL signal.
        4.  **Confirm with Parabolic SAR:** The `sar` should align with your intended direction. For a BUY signal, the SAR must be below the `current_price`. For a SELL signal, it must be above.
        5.  **Synthesize and Decide:** Make your final decision (BUY, SELL, HOLD) based on the **confluence** of these signals. A high-confidence trade occurs when the trend, momentum, and entry point signals all align. Provide a step-by-step reasoning for your conclusion.

        Based on all the data, provide your structured analysis now.
        """
        return ChatPromptTemplate.from_template(template=prompt_str,
            partial_variables={
                "format_instructions": self.entry_parser.get_format_instructions()
            },
        )



    def _create_position_prompt_template(self):
        prompt_str = """
        You are an expert technical analyst for a quantitative trading bot. Your task is to analyze a complete snapshot of market indicators for {trading_pair} and provide a structured recommendation for an EXISTING position: BUY, SELL, or HOLD.

        **Format Instructions:**
        {format_instructions}

        **Market Data Report:**
        - **Price Indicators:**
        - Current Price: {current_price}
        - Average Price (Period): {avg_price}
        - Median Price (Period): {median_price}
        - **Trend & Overlap Studies:**
        - EMA (200p): {ema}
        - SMA (Period): {sma}
        - Parabolic SAR: {sar}
        - Bollinger Bands:
            - Upper: {bband_upper}
            - Middle: {bband_middle}
            - Lower: {bband_lower}
        - **Momentum Indicators:**
        - RSI (14p): {rsi}
        - **Volatility Indicators:**
        - ATR (14p): {atr}
        - News: {news_headlines}
        - Market Sentiment: {sentiment_score}

        **Decision-Making Heuristics:**
        1.  **Determine the Main Trend:** First, compare the `current_price` to the `ema`. A price above the EMA suggests a general uptrend (favorable for BUYs). A price below suggests a downtrend (favorable for SELLs or HOLD).
        2.  **Assess Momentum:** Check the `rsi`. An RSI below 35 suggests oversold conditions (potential BUY opportunity). An RSI above 65 suggests overbought conditions (potential SELL opportunity).
        3.  **Identify Entry/Exit Points:** Look at the Bollinger Bands. The price touching the `lower` band is a strong support level and reinforces a BUY signal, especially in an uptrend. The price touching the `upper` band reinforces a SELL signal.
        4.  **Confirm with Parabolic SAR:** The `sar` should align with your intended direction. For a BUY signal, the SAR must be below the `current_price`. For a SELL signal, it must be above.
        5.  **Synthesize and Decide:** Make your final decision (BUY, SELL, HOLD) based on the **confluence** of these signals. A high-confidence trade occurs when the trend, momentum, and entry point signals all align. Provide a step-by-step reasoning for your conclusion.

        Based on all the data, provide your structured analysis now.
        """
        return ChatPromptTemplate.from_template(template=prompt_str,
            partial_variables={
                "format_instructions": self.entry_parser.get_format_instructions()
            },
        )

    def analyze_for_new_entry(self, market_indicators: MarketIndicators):
        """Queries the LLM for analysis on whether to enter a new trade."""
        if not self.config["ai_enabled"]:
            return {"market_sentiment": "Neutral", "is_entry_favorable": False}

        logger.info("Querying AI for new entry analysis...")

        # Get market context with caching and truncation
        market_context = self._get_market_context()

        prompt_data = {
            "trading_pair": self.config["trading_pair"],
            "current_price": market_indicators.price_indicators.current_price,
            "avg_price": market_indicators.price_indicators.avg_price,
            "median_price": market_indicators.price_indicators.median_price,
            "ema": market_indicators.overlap_studies.ema,
            "sma": market_indicators.overlap_studies.sma,
            "sar": market_indicators.overlap_studies.sar,
            "bband_upper": market_indicators.overlap_studies.bbands["upper"],
            "bband_middle": market_indicators.overlap_studies.bbands["middle"],
            "bband_lower": market_indicators.overlap_studies.bbands["lower"],
            "rsi": market_indicators.momentum_indicators.rsi,
            "atr": market_indicators.volatility_indicators.atr,
            "news_headlines": market_context["news_headlines"],
            "sentiment_score": market_context["sentiment_score"],
        }

        chain = self.entry_prompt | self.llm | self.entry_parser
        try:
            result = chain.invoke(prompt_data)
            favorable_sentiments = ["Bullish", "Very Bullish"]
            result["is_entry_favorable"] = (
                result["market_sentiment"] in favorable_sentiments
                and result["confidence_score"] >= 0.6
            )
            logger.info(f"AI Entry Analysis received: {result}")
            return result
        except Exception as e:
            logger.error(f"Error calling AI for entry analysis: {e}")
            return {"market_sentiment": "Neutral", "is_entry_favorable": False}

    def analyze_open_position(self, position, market_indicators: MarketIndicators):
        """Queries the LLM for analysis on an existing trade."""
        if not self.config["ai_enabled"]:
            return {"action": "hold", "reasoning": "AI disabled."}

        current_price = float(current_price)
        logger.info("Querying AI for open position analysis...")

        pnl_percentage = (
            (current_price - position["average_price"]) / position["average_price"]
        ) * 100

         # Get market context with caching and truncation
        market_context = self._get_market_context()

        prompt_data = {
            "entry_price": position["entry_price"],
            "take_profit_price": position["take_profit_price"],
            "trailing_stop_price": position["trailing_stop_price"],
            "pnl_percentage": f"{pnl_percentage:.2f}",
            "trading_pair": self.config["trading_pair"],
            "current_price": market_indicators.price_indicators.current_price,
            "avg_price": market_indicators.price_indicators.avg_price,
            "median_price": market_indicators.price_indicators.median_price,
            "ema": market_indicators.overlap_studies.ema,
            "sma": market_indicators.overlap_studies.sma,
            "sar": market_indicators.overlap_studies.sar,
            "bband_upper": market_indicators.overlap_studies.bbands["upper"],
            "bband_middle": market_indicators.overlap_studies.bbands["middle"],
            "bband_lower": market_indicators.overlap_studies.bbands["lower"],
            "rsi": market_indicators.momentum_indicators.rsi,
            "atr": market_indicators.volatility_indicators.atr,
            "news_headlines": market_context["news_headlines"],
            "sentiment_score": market_context["sentiment_score"],
        }


        chain = self.position_prompt | self.llm | self.position_parser
        try:
            result = chain.invoke(prompt_data)
            logger.info(f"AI Position Analysis received: {result}")
            return result
        except Exception as e:
            logger.error(f"Error calling AI for position analysis: {e}")
            return {"action": "hold", "reasoning": "AI call failed."}

    def _truncate_context(self, text: str, max_length: int = None) -> str:
        """Truncate text to avoid context overflow while preserving important information."""
        if max_length is None:
            max_length = self.max_context_length

        if len(text) <= max_length:
            return text

        # Try to truncate at sentence boundaries
        sentences = text.split(". ")
        truncated = ""

        for sentence in sentences:
            if len(truncated + sentence + ". ") <= max_length - 50:  # Leave some buffer
                truncated += sentence + ". "
            else:
                break

        if not truncated:  # If no complete sentences fit, just truncate
            truncated = text[: max_length - 3] + "..."
        else:
            truncated += "..."

        return truncated

    def _get_cached_context(self, cache_key: str) -> Optional[Dict]:
        """Get cached context if still valid."""
        if cache_key in self.context_cache:
            cached_data, timestamp = self.context_cache[cache_key]
            if time.time() - timestamp < self.cache_duration:
                return cached_data
            else:
                # Remove expired cache
                del self.context_cache[cache_key]
        return None

    def _cache_context(self, cache_key: str, data: Dict):
        """Cache context data with timestamp."""
        self.context_cache[cache_key] = (data, time.time())

    def _get_market_context(
        self, include_news: bool = True, include_sentiment: bool = True
    ) -> Dict:
        """Get comprehensive market context with caching and truncation."""
        cache_key = f"market_context_{include_news}_{include_sentiment}"

        # Try to get from cache first
        cached = self._get_cached_context(cache_key)
        if cached:
            logger.debug("Using cached market context")
            return cached

        context = {}

        # Get news headlines
        if include_news:
            try:
                query = '("cryptocurrency" OR "crypto" OR "blockchain" OR "bitcoin" OR "ethereum" OR "altcoin" OR "DeFi" OR "NFT" OR "stablecoin") AND ("price" OR "market" OR "regulation" OR "update" OR "analysis" OR "trading" OR "news")'
                news_headlines = self.news_fetcher.get_news(query)
                if news_headlines:
                    # Truncate news to avoid overflow
                    news_text = str(news_headlines)
                    context["news_headlines"] = self._truncate_context(news_text, 1000)
                else:
                    context["news_headlines"] = "No recent news available"
            except Exception as e:
                logger.error(f"Error fetching news: {e}")
                context["news_headlines"] = "Error fetching news"

        # Get sentiment data
        if include_sentiment:
            try:
                fear_greed = fg.get_fear_and_greed_index()
                if fear_greed:
                    context["sentiment_score"] = (
                        f"{fear_greed.get('value', 'N/A')} ({fear_greed.get('value_classification', 'Unknown')})"
                    )
                else:
                    context["sentiment_score"] = "Sentiment data unavailable"
            except Exception as e:
                logger.error(f"Error fetching sentiment: {e}")
                context["sentiment_score"] = "Error fetching sentiment"

        # Cache the context
        self._cache_context(cache_key, context)

        return context

    def analyze_startup_position(
        self, position: Dict, current_price: float, rsi: float
    ) -> Dict:
        """Analyze existing position when bot starts up."""
        if not self.config["ai_enabled"]:
            return {"action": "hold", "reasoning": "AI disabled."}

        logger.info("Analyzing existing position at startup...")

        current_price = float(current_price)
        pnl_percentage = (
            (current_price - position["average_price"]) / position["average_price"]
        ) * 100

        # Get market context with truncation
        market_context = self._get_market_context()

        # Calculate position metrics
        holding_time_hours = 0
        if position.get("last_ai_check_ts"):
            holding_time_hours = (time.time() - position["last_ai_check_ts"]) / 3600

        # Prepare market data with context management
        market_data = {
            "trading_pair": self.config["trading_pair"],
            "average_price": position["average_price"],
            "pnl_percentage": f"{pnl_percentage:.2f}",
            "dca_orders": len(position["dca_orders"]),
            "current_price": current_price,
            "holding_time_hours": f"{holding_time_hours:.1f}",
            "rsi": str(rsi),
            "startup_analysis": "true",
        }

        # Add market context
        market_data.update(market_context)

        # Enhanced position prompt for startup
        startup_prompt_str = """
        You are analyzing an EXISTING position at bot startup for {trading_pair}.
        This position was opened previously and you need to assess its current status.

        **Format Instructions:**
        {format_instructions}

        **Position Status:**
        - Entry Price: ${average_price}
        - Current Price: ${current_price}
        - Current P&L: {pnl_percentage}%
        - DCA Orders Used: {dca_orders}
        - Holding Time: {holding_time_hours} hours
        - RSI: {rsi}

        **Market Context:**
        - Recent News: {news_headlines}
        - Market Sentiment: {sentiment_score}

        **Special Instructions for Startup Analysis:**
        - Consider if market conditions have changed significantly
        - Evaluate if the position still aligns with current market sentiment
        - Be more conservative with sell recommendations at startup
        - Consider the holding time when making recommendations

        Provide your analysis based on the current market conditions and position status.
        """

        startup_prompt = ChatPromptTemplate.from_template(
            template=startup_prompt_str,
            partial_variables={
                "format_instructions": self.position_parser.get_format_instructions()
            },
        )

        chain = startup_prompt | self.llm | self.position_parser

        try:
            result = chain.invoke(market_data)
            result["startup_analysis"] = True
            logger.info(f"Startup position analysis completed: {result}")
            return result
        except Exception as e:
            logger.error(f"Error in startup position analysis: {e}")
            return {
                "action": "hold",
                "reasoning": "Startup analysis failed, holding position.",
            }

    def get_context_summary(self) -> Dict:
        """Get a summary of current context cache status."""
        return {
            "cached_items": len(self.context_cache),
            "cache_duration": self.cache_duration,
            "max_context_length": self.max_context_length,
            "cache_keys": list(self.context_cache.keys()),
        }
