"""
Prompt Templates for AI Trading Analysis

This module contains all prompt templates used by the AI engine for trading analysis.
Templates are organized by functionality and provide consistent, maintainable prompts.

Author: Senior Python Developer
"""

from typing import Dict, Any
from dataclasses import dataclass
from enum import Enum


class PromptType(Enum):
    """Enumeration of available prompt types."""
    ENTRY_ANALYSIS = "entry_analysis"
    POSITION_ANALYSIS = "position_analysis"
    STARTUP_ANALYSIS = "startup_analysis"


@dataclass
class PromptTemplate:
    """
    Data class representing a prompt template with metadata.

    Attributes:
        template: The actual prompt template string
        description: Human-readable description of the template's purpose
        required_variables: Set of required template variables
        optional_variables: Set of optional template variables
    """
    template: str
    description: str
    required_variables: set
    optional_variables: set = None

    def __post_init__(self):
        if self.optional_variables is None:
            self.optional_variables = set()


class TradingPromptTemplates:
    """
    Central repository for all trading-related prompt templates.

    This class provides a clean interface to access prompt templates
    with proper validation and documentation.
    """

    # Common market data variables used across templates
    COMMON_MARKET_VARIABLES = {
        "trading_pair", "current_price", "avg_price", "median_price",
        "ema", "sma", "sar", "bband_upper", "bband_middle", "bband_lower",
        "rsi", "atr", "news_headlines", "sentiment_score"
    }

    # Common decision-making heuristics used in analysis
    DECISION_HEURISTICS = """
        **Decision-Making Heuristics:**
        1.  **Determine the Main Trend:** First, compare the `current_price` to the `ema`. A price above the EMA suggests a general uptrend (favorable for BUYs). A price below suggests a downtrend (favorable for SELLs or HOLD).
        2.  **Assess Momentum:** Check the `rsi`. An RSI below 35 suggests oversold conditions (potential BUY opportunity). An RSI above 65 suggests overbought conditions (potential SELL opportunity).
        3.  **Identify Entry/Exit Points:** Look at the Bollinger Bands. The price touching the `lower` band is a strong support level and reinforces a BUY signal, especially in an uptrend. The price touching the `upper` band reinforces a SELL signal.
        4.  **Confirm with Parabolic SAR:** The `sar` should align with your intended direction. For a BUY signal, the SAR must be below the `current_price`. For a SELL signal, it must be above.
        5.  **Synthesize and Decide:** Make your final decision (BUY, SELL, HOLD) based on the **confluence** of these signals. A high-confidence trade occurs when the trend, momentum, and entry point signals all align. Provide a step-by-step reasoning for your conclusion.
    """

    # Market data report template used across different prompts
    MARKET_DATA_REPORT = """
        **Market Data Report:**
        - Trading Pair: {{trading_pair}}
        - Market data: {{market_data}}
    """

    @classmethod
    def get_entry_analysis_template(cls) -> PromptTemplate:
        """
        Get the template for analyzing new entry opportunities.

        Returns:
            PromptTemplate: Template for entry analysis
        """
        template = f"""
        You are an expert technical analyst for a quantitative trading bot. Your task is to analyze a complete snapshot of market indicators for {{trading_pair}} and provide a structured recommendation for a NEW position: BUY or WAIT for better indicators.

        **Format Instructions:**
        {{format_instructions}}

        {cls.MARKET_DATA_REPORT}

        {cls.DECISION_HEURISTICS}

        Based on all the data, provide your structured analysis now.
        """

        return PromptTemplate(
            template=template.strip(),
            description="Template for analyzing new entry opportunities in the market",
            required_variables=cls.COMMON_MARKET_VARIABLES | {"format_instructions"},
            optional_variables=set()
        )

    @classmethod
    def get_position_analysis_template(cls) -> PromptTemplate:
        """
        Get the template for analyzing existing positions.

        Returns:
            PromptTemplate: Template for position analysis
        """
        template = f"""
        You are an expert technical analyst for a quantitative trading bot. Your task is to analyze a complete snapshot of market indicators for {{trading_pair}} and provide a structured recommendation for an EXISTING position: BUY, SELL, or HOLD.

        **Format Instructions:**
        {{format_instructions}}

        {cls.MARKET_DATA_REPORT}

        {cls.DECISION_HEURISTICS}

        Based on all the data, provide your structured analysis now.
        """

        return PromptTemplate(
            template=template.strip(),
            description="Template for analyzing existing trading positions",
            required_variables=cls.COMMON_MARKET_VARIABLES | {"format_instructions"},
            optional_variables=set()
        )

    @classmethod
    def get_startup_analysis_template(cls) -> PromptTemplate:
        """
        Get the template for analyzing positions at bot startup.

        Returns:
            PromptTemplate: Template for startup position analysis
        """
        template = """
        You are analyzing an EXISTING position at bot startup for {trading_pair}.
        This position was opened previously and you need to assess its current status.

        **Format Instructions:**
        {format_instructions}

        **Position Status:**
        - Entry Price: ${average_price}
        - Current Price: ${current_price}
        - Current P&L: {pnl_percentage}%
        - DCA Orders Used: {dca_orders}
        - Holding Time: {holding_time_hours} hours
        - RSI: {rsi}

        **Market Context:**
        - Recent News: {news_headlines}
        - Market Sentiment: {sentiment_score}

        **Special Instructions for Startup Analysis:**
        - Consider if market conditions have changed significantly
        - Evaluate if the position still aligns with current market sentiment
        - Be more conservative with sell recommendations at startup
        - Consider the holding time when making recommendations

        Provide your analysis based on the current market conditions and position status.
        """

        return PromptTemplate(
            template=template.strip(),
            description="Template for analyzing existing positions at bot startup",
            required_variables={
                "trading_pair", "format_instructions", "average_price",
                "current_price", "pnl_percentage", "dca_orders",
                "holding_time_hours", "rsi", "news_headlines", "sentiment_score"
            },
            optional_variables=set()
        )

    @classmethod
    def get_template(cls, prompt_type: PromptType) -> PromptTemplate:
        """
        Get a template by its type.

        Args:
            prompt_type: The type of prompt template to retrieve

        Returns:
            PromptTemplate: The requested template

        Raises:
            ValueError: If the prompt type is not supported
        """
        template_map = {
            PromptType.ENTRY_ANALYSIS: cls.get_entry_analysis_template,
            PromptType.POSITION_ANALYSIS: cls.get_position_analysis_template,
            PromptType.STARTUP_ANALYSIS: cls.get_startup_analysis_template,
        }

        if prompt_type not in template_map:
            raise ValueError(f"Unsupported prompt type: {prompt_type}")

        return template_map[prompt_type]()

    @classmethod
    def validate_template_variables(cls, template: PromptTemplate, variables: Dict[str, Any]) -> bool:
        """
        Validate that all required variables are present in the provided variables dict.

        Args:
            template: The template to validate against
            variables: Dictionary of variables to check

        Returns:
            bool: True if all required variables are present

        Raises:
            ValueError: If required variables are missing
        """
        provided_vars = set(variables.keys())
        missing_vars = template.required_variables - provided_vars

        if missing_vars:
            raise ValueError(
                f"Missing required template variables: {missing_vars}. "
                f"Required: {template.required_variables}, "
                f"Provided: {provided_vars}"
            )

        return True

    @classmethod
    def get_available_templates(cls) -> Dict[str, str]:
        """
        Get a dictionary of available templates with their descriptions.

        Returns:
            Dict[str, str]: Mapping of template names to descriptions
        """
        return {
            PromptType.ENTRY_ANALYSIS.value: cls.get_entry_analysis_template().description,
            PromptType.POSITION_ANALYSIS.value: cls.get_position_analysis_template().description,
            PromptType.STARTUP_ANALYSIS.value: cls.get_startup_analysis_template().description,
        }


# Convenience functions for backward compatibility and ease of use
def get_entry_analysis_prompt() -> str:
    """Get the entry analysis prompt template string."""
    return TradingPromptTemplates.get_entry_analysis_template().template


def get_position_analysis_prompt() -> str:
    """Get the position analysis prompt template string."""
    return TradingPromptTemplates.get_position_analysis_template().template


def get_startup_analysis_prompt() -> str:
    """Get the startup analysis prompt template string."""
    return TradingPromptTemplates.get_startup_analysis_template().template