from typing import Optional
from pydantic import BaseModel, Field
from models.Indicators.MomentumIndicators import MomentumIndicators
from models.Indicators.OverlapStudies import OverlapStudies
from models.Indicators.PriceIndicators import PriceIndicators
from models.Indicators.VolatilityIndicators import VolatilityIndicators


class MarketIndicators(BaseModel):
    overlap_studies: OverlapStudies = Field(default_factory=OverlapStudies)
    momentum_indicators: MomentumIndicators = Field(default_factory=MomentumIndicators)
    volatility_indicators: VolatilityIndicators = Field(default_factory=VolatilityIndicators)
    price_indicators: PriceIndicators = Field(default_factory=PriceIndicators)

    def is_trending_up(self) -> bool:
        """Un método simple para comprobar si el precio está por encima de la EMA."""
        if (
            self.price_indicators.current_price is not None
            and self.overlap_studies.ema is not None
        ):
            return self.price_indicators.current_price > self.overlap_studies.ema
        return False
